package com.psychology.counseling.dto;

import com.psychology.counseling.entity.ConsultationType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class AppointmentDto {
    
    @NotNull(message = "咨询师ID不能为空")
    private Long counselorId;
    
    @NotNull(message = "预约时间不能为空")
    private LocalDateTime appointmentTime;
    
    private Integer durationMinutes = 60;
    
    private ConsultationType consultationType = ConsultationType.ONLINE;
    
    private String description;
    
    private String userNotes;
}
