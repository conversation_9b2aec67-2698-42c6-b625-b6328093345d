package com.psychology.counseling.dto;

import com.psychology.counseling.entity.ReplyType;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class PostReplyDto {
    
    @NotNull(message = "帖子ID不能为空")
    private Long postId;
    
    @NotBlank(message = "回复内容不能为空")
    @Size(max = 500, message = "回复内容长度不能超过500个字符")
    private String content;
    
    private String anonymousNickname;
    
    private ReplyType replyType = ReplyType.SUPPORT;
    
    private Boolean isAnonymous = true; // 是否匿名回复
}
