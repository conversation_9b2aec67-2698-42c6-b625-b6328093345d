<template>
  <div class="home">
    <!-- 欢迎横幅 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1>心理咨询与匿名倾诉平台</h1>
        <p>在这里，您可以安全地分享内心的声音，获得专业的心理支持</p>
        <div class="hero-buttons">
          <el-button type="primary" size="large" @click="$router.push('/anonymous-wall')">
            进入倾诉墙
          </el-button>
          <el-button type="success" size="large" @click="$router.push('/appointment')" v-if="isLoggedIn">
            预约咨询
          </el-button>
          <el-button type="success" size="large" @click="$router.push('/login')" v-else>
            立即登录
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 功能介绍 -->
    <div class="features-section">
      <div class="container">
        <h2>平台特色</h2>
        <el-row :gutter="30">
          <el-col :xs="24" :sm="12" :md="8">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="el-icon-chat-line-round"></i>
              </div>
              <h3>匿名倾诉</h3>
              <p>完全匿名的倾诉环境，让您安心分享内心的困扰和情感</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="el-icon-user-solid"></i>
              </div>
              <h3>专业咨询</h3>
              <p>资深心理咨询师提供一对一专业咨询服务</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="el-icon-star-on"></i>
              </div>
              <h3>温暖支持</h3>
              <p>来自社区的温暖回复和鼓励，让您不再孤单</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    
    <!-- 最新倾诉 -->
    <div class="recent-posts-section">
      <div class="container">
        <h2>最新倾诉</h2>
        <div class="posts-preview">
          <div v-for="post in recentPosts" :key="post.id" class="post-preview">
            <div class="post-header">
              <span class="post-author">{{ post.anonymousNickname }}</span>
              <span class="post-time">{{ formatTime(post.createdAt) }}</span>
            </div>
            <h4 class="post-title">{{ post.title }}</h4>
            <p class="post-content">{{ post.content.substring(0, 100) }}...</p>
            <div class="post-stats">
              <span><i class="el-icon-view"></i> {{ post.viewCount }}</span>
              <span><i class="el-icon-chat-line-square"></i> {{ post.replyCount }}</span>
              <span><i class="el-icon-star-on"></i> {{ post.likeCount }}</span>
            </div>
          </div>
        </div>
        <div class="view-more">
          <el-button type="primary" @click="$router.push('/anonymous-wall')">
            查看更多倾诉
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import moment from 'moment'

export default {
  name: 'Home',
  data() {
    return {
      recentPosts: []
    }
  },
  computed: {
    ...mapGetters(['isLoggedIn'])
  },
  mounted() {
    this.loadRecentPosts()
  },
  methods: {
    async loadRecentPosts() {
      try {
        const response = await this.$http.get('/posts?page=0&size=3')
        if (response.success) {
          this.recentPosts = response.data.content
        }
      } catch (error) {
        console.error('加载最新倾诉失败:', error)
      }
    },
    formatTime(time) {
      return moment(time).fromNow()
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.hero-content h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 300;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.hero-buttons .el-button {
  margin: 0 10px;
}

.features-section {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.features-section h2 {
  text-align: center;
  margin-bottom: 50px;
  font-size: 2.5rem;
  color: #333;
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  height: 100%;
}

.feature-icon {
  font-size: 3rem;
  color: #409EFF;
  margin-bottom: 20px;
}

.feature-card h3 {
  margin-bottom: 15px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

.recent-posts-section {
  padding: 80px 0;
}

.recent-posts-section h2 {
  text-align: center;
  margin-bottom: 50px;
  font-size: 2.5rem;
  color: #333;
}

.posts-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.post-preview {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  border-left: 4px solid #409EFF;
}

.post-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #666;
}

.post-title {
  margin-bottom: 10px;
  color: #333;
  font-size: 1.1rem;
}

.post-content {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.post-stats {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
  color: #999;
}

.post-stats span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.view-more {
  text-align: center;
}
</style>
