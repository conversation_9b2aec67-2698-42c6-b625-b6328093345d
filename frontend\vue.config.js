module.exports = {
  devServer: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  },
  
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  
  chainWebpack: config => {
    config.plugin('html').tap(args => {
      args[0].title = '心理咨询与匿名倾诉平台'
      return args
    })
  }
}
