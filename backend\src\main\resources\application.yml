server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: counseling-platform
  
  # 数据库配置
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# JWT配置
jwt:
  secret: mySecretKey
  expiration: 86400000 # 24小时

# 日志配置
logging:
  level:
    com.psychology.counseling: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
