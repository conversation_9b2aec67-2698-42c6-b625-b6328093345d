package com.psychology.counseling.repository;

import com.psychology.counseling.entity.User;
import com.psychology.counseling.entity.UserRole;
import com.psychology.counseling.entity.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    Optional<User> findByUsername(String username);
    
    Optional<User> findByEmail(String email);
    
    boolean existsByUsername(String username);
    
    boolean existsByEmail(String email);
    
    List<User> findByRole(UserRole role);
    
    List<User> findByStatus(UserStatus status);
    
    Page<User> findByRole(UserRole role, Pageable pageable);
    
    @Query("SELECT u FROM User u WHERE u.role = :role AND u.status = :status AND u.isAvailable = true")
    List<User> findAvailableCounselors(@Param("role") UserRole role, @Param("status") UserStatus status);
    
    @Query("SELECT u FROM User u WHERE u.role = 'COUNSELOR' AND u.status = 'ACTIVE' AND u.isAvailable = true")
    List<User> findAvailableCounselors();
    
    @Query("SELECT u FROM User u WHERE u.username LIKE %:keyword% OR u.realName LIKE %:keyword% OR u.email LIKE %:keyword%")
    Page<User> searchUsers(@Param("keyword") String keyword, Pageable pageable);
}
