package com.psychology.counseling.dto;

import com.psychology.counseling.entity.UserRole;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class UserRegistrationDto {
    
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, message = "密码长度至少6个字符")
    private String password;
    
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    private String phoneNumber;
    
    private String realName;
    
    private UserRole role = UserRole.USER;
    
    // 咨询师注册时的额外字段
    private String professionalTitle;
    private String qualificationCertificate;
    private Integer yearsOfExperience;
    private String specialization;
    private Double consultationFee;
}
