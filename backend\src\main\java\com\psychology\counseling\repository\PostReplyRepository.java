package com.psychology.counseling.repository;

import com.psychology.counseling.entity.PostReply;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PostReplyRepository extends JpaRepository<PostReply, Long> {
    
    List<PostReply> findByPostIdOrderByCreatedAtAsc(Long postId);
    
    Page<PostReply> findByPostIdOrderByCreatedAtAsc(Long postId, Pageable pageable);
    
    List<PostReply> findByReplierIdOrderByCreatedAtDesc(Long replierId);
    
    @Query("SELECT r FROM PostReply r WHERE r.isCounselorReply = true AND r.post.id = :postId ORDER BY r.createdAt ASC")
    List<PostReply> findCounselorRepliesByPostId(@Param("postId") Long postId);
    
    @Modifying
    @Query("UPDATE PostReply r SET r.likeCount = r.likeCount + 1 WHERE r.id = :id")
    void incrementLikeCount(@Param("id") Long id);
    
    Long countByPostId(Long postId);
    
    @Query("SELECT COUNT(r) FROM PostReply r WHERE r.post.id = :postId AND r.isCounselorReply = true")
    Long countCounselorRepliesByPostId(@Param("postId") Long postId);
}
