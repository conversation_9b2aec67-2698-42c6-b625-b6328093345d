package com.psychology.counseling.dto;

import com.psychology.counseling.entity.EmotionTag;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class AnonymousPostDto {
    
    @NotBlank(message = "标题不能为空")
    @Size(max = 100, message = "标题长度不能超过100个字符")
    private String title;
    
    @NotBlank(message = "内容不能为空")
    @Size(max = 2000, message = "内容长度不能超过2000个字符")
    private String content;
    
    private String anonymousNickname;
    
    private EmotionTag emotionTag;
    
    private Boolean isAnonymous = true; // 是否完全匿名（不关联用户ID）
}
