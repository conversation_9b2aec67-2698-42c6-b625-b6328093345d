<template>
  <div id="app">
    <el-container>
      <!-- 顶部导航栏 -->
      <el-header v-if="showHeader">
        <nav-bar />
      </el-header>
      
      <!-- 主要内容区域 -->
      <el-main>
        <router-view />
      </el-main>
      
      <!-- 底部 -->
      <el-footer v-if="showFooter">
        <div class="footer-content">
          <p>&copy; 2024 心理咨询与匿名倾诉平台. All rights reserved.</p>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import NavBar from './components/NavBar.vue'

export default {
  name: 'App',
  components: {
    NavBar
  },
  computed: {
    showHeader() {
      return this.$route.path !== '/login' && this.$route.path !== '/register'
    },
    showFooter() {
      return this.$route.path !== '/login' && this.$route.path !== '/register'
    }
  }
}
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100vh;
}

.el-header {
  background-color: #409EFF;
  color: white;
  padding: 0;
}

.el-main {
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.el-footer {
  background-color: #f5f5f5;
  color: #666;
  text-align: center;
  line-height: 60px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
