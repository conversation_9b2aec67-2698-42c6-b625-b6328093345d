<template>
  <div class="appointment">
    <div class="container">
      <div class="page-header">
        <h1>预约咨询</h1>
        <p>选择合适的心理咨询师，预约专业的心理咨询服务</p>
      </div>
      
      <!-- 咨询师列表 -->
      <div class="counselors-section">
        <h2>可预约的咨询师</h2>
        <div v-loading="loading" class="counselors-list">
          <div v-for="counselor in counselors" :key="counselor.id" class="counselor-card">
            <el-card>
              <div class="counselor-info">
                <div class="counselor-avatar">
                  <img v-if="counselor.avatarUrl" :src="counselor.avatarUrl" :alt="counselor.realName" />
                  <i v-else class="el-icon-user-solid"></i>
                </div>
                <div class="counselor-details">
                  <h3>{{ counselor.realName || counselor.username }}</h3>
                  <p class="title">{{ counselor.professionalTitle }}</p>
                  <p class="experience">从业经验：{{ counselor.yearsOfExperience }}年</p>
                  <p class="specialization">专业领域：{{ counselor.specialization }}</p>
                  <p class="fee">咨询费用：¥{{ counselor.consultationFee }}/小时</p>
                </div>
                <div class="counselor-actions">
                  <el-button type="primary" @click="selectCounselor(counselor)">
                    选择预约
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
      
      <!-- 预约表单 -->
      <div v-if="selectedCounselor" class="appointment-form-section">
        <el-card>
          <div slot="header">
            <span>预约 {{ selectedCounselor.realName || selectedCounselor.username }}</span>
          </div>
          
          <el-form ref="appointmentForm" :model="appointmentForm" :rules="appointmentRules" label-width="120px">
            <el-form-item label="预约时间" prop="appointmentTime">
              <el-date-picker
                v-model="appointmentForm.appointmentTime"
                type="datetime"
                placeholder="选择预约时间"
                :picker-options="pickerOptions"
                style="width: 100%"
              />
            </el-form-item>
            
            <el-form-item label="咨询时长" prop="durationMinutes">
              <el-select v-model="appointmentForm.durationMinutes" placeholder="选择咨询时长">
                <el-option label="60分钟" :value="60" />
                <el-option label="90分钟" :value="90" />
                <el-option label="120分钟" :value="120" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="咨询方式" prop="consultationType">
              <el-select v-model="appointmentForm.consultationType" placeholder="选择咨询方式">
                <el-option label="在线咨询" value="ONLINE" />
                <el-option label="视频咨询" value="VIDEO" />
                <el-option label="电话咨询" value="PHONE" />
                <el-option label="线下咨询" value="OFFLINE" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="问题描述" prop="description">
              <el-input
                v-model="appointmentForm.description"
                type="textarea"
                placeholder="请简要描述您希望咨询的问题..."
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="备注" prop="userNotes">
              <el-input
                v-model="appointmentForm.userNotes"
                type="textarea"
                placeholder="其他需要说明的事项..."
                :rows="3"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :loading="submitting" @click="submitAppointment">
                提交预约
              </el-button>
              <el-button @click="cancelAppointment">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Appointment',
  data() {
    return {
      loading: false,
      submitting: false,
      counselors: [],
      selectedCounselor: null,
      appointmentForm: {
        counselorId: null,
        appointmentTime: null,
        durationMinutes: 60,
        consultationType: 'ONLINE',
        description: '',
        userNotes: ''
      },
      appointmentRules: {
        appointmentTime: [
          { required: true, message: '请选择预约时间', trigger: 'change' }
        ],
        durationMinutes: [
          { required: true, message: '请选择咨询时长', trigger: 'change' }
        ],
        consultationType: [
          { required: true, message: '请选择咨询方式', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请描述您的问题', trigger: 'blur' },
          { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }
        ]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择过去的日期
        },
        disabledTime(time) {
          const hour = time.getHours()
          return {
            disabledHours: () => {
              const hours = []
              for (let i = 0; i < 9; i++) hours.push(i) // 9点前不可选
              for (let i = 22; i < 24; i++) hours.push(i) // 22点后不可选
              return hours
            }
          }
        }
      }
    }
  },
  mounted() {
    this.loadCounselors()
  },
  methods: {
    async loadCounselors() {
      this.loading = true
      try {
        // 这里应该调用获取可用咨询师的API
        // 暂时使用模拟数据
        this.counselors = [
          {
            id: 1,
            username: 'counselor1',
            realName: '张心理',
            professionalTitle: '国家二级心理咨询师',
            yearsOfExperience: 8,
            specialization: '焦虑症、抑郁症、人际关系',
            consultationFee: 300,
            avatarUrl: null
          },
          {
            id: 2,
            username: 'counselor2',
            realName: '李咨询',
            professionalTitle: '临床心理学硕士',
            yearsOfExperience: 5,
            specialization: '青少年心理、学习压力、情感问题',
            consultationFee: 250,
            avatarUrl: null
          }
        ]
      } catch (error) {
        this.$message.error('加载咨询师列表失败')
      } finally {
        this.loading = false
      }
    },
    
    selectCounselor(counselor) {
      this.selectedCounselor = counselor
      this.appointmentForm.counselorId = counselor.id
    },
    
    submitAppointment() {
      this.$refs.appointmentForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          try {
            // 这里应该调用预约API
            // const response = await this.$http.post('/appointments', this.appointmentForm)
            this.$message.success('预约提交成功，请等待咨询师确认')
            this.cancelAppointment()
          } catch (error) {
            this.$message.error('预约失败，请重试')
          } finally {
            this.submitting = false
          }
        }
      })
    },
    
    cancelAppointment() {
      this.selectedCounselor = null
      this.$refs.appointmentForm.resetFields()
      this.appointmentForm = {
        counselorId: null,
        appointmentTime: null,
        durationMinutes: 60,
        consultationType: 'ONLINE',
        description: '',
        userNotes: ''
      }
    }
  }
}
</script>

<style scoped>
.appointment {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  color: #333;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

.counselors-section {
  margin-bottom: 40px;
}

.counselors-section h2 {
  color: #333;
  margin-bottom: 20px;
}

.counselors-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.counselor-card {
  height: 100%;
}

.counselor-info {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.counselor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.counselor-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.counselor-avatar i {
  font-size: 40px;
  color: #ccc;
}

.counselor-details {
  flex: 1;
}

.counselor-details h3 {
  color: #333;
  margin-bottom: 8px;
  font-size: 1.2rem;
}

.counselor-details p {
  color: #666;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.counselor-details .title {
  color: #409EFF;
  font-weight: 500;
}

.counselor-details .fee {
  color: #E6A23C;
  font-weight: 500;
}

.counselor-actions {
  flex-shrink: 0;
}

.appointment-form-section {
  max-width: 800px;
  margin: 0 auto;
}
</style>
