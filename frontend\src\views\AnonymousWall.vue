<template>
  <div class="anonymous-wall">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>匿名倾诉墙</h1>
        <p>在这里，您可以安全地分享内心的声音</p>
      </div>
      
      <!-- 发布新倾诉 -->
      <div class="post-form-section">
        <el-card class="post-form-card">
          <div slot="header">
            <span>分享您的心声</span>
          </div>
          
          <el-form ref="postForm" :model="postForm" :rules="postRules" label-width="80px">
            <el-form-item label="标题" prop="title">
              <el-input
                v-model="postForm.title"
                placeholder="请输入标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="内容" prop="content">
              <el-input
                v-model="postForm.content"
                type="textarea"
                placeholder="请分享您的心声..."
                :rows="6"
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="情感标签" prop="emotionTag">
              <el-select v-model="postForm.emotionTag" placeholder="选择情感标签">
                <el-option label="抑郁" value="DEPRESSION" />
                <el-option label="焦虑" value="ANXIETY" />
                <el-option label="压力" value="STRESS" />
                <el-option label="孤独" value="LONELINESS" />
                <el-option label="愤怒" value="ANGER" />
                <el-option label="困惑" value="CONFUSION" />
                <el-option label="悲伤" value="SADNESS" />
                <el-option label="恐惧" value="FEAR" />
                <el-option label="希望" value="HOPE" />
                <el-option label="感激" value="GRATITUDE" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="昵称" prop="anonymousNickname">
              <el-input
                v-model="postForm.anonymousNickname"
                placeholder="匿名昵称（可选）"
                maxlength="20"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :loading="publishing" @click="publishPost">
                发布倾诉
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
      
      <!-- 筛选和搜索 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-select v-model="filterEmotion" placeholder="按情感筛选" @change="loadPosts">
              <el-option label="全部" value="" />
              <el-option label="抑郁" value="DEPRESSION" />
              <el-option label="焦虑" value="ANXIETY" />
              <el-option label="压力" value="STRESS" />
              <el-option label="孤独" value="LONELINESS" />
              <el-option label="愤怒" value="ANGER" />
              <el-option label="困惑" value="CONFUSION" />
              <el-option label="悲伤" value="SADNESS" />
              <el-option label="恐惧" value="FEAR" />
              <el-option label="希望" value="HOPE" />
              <el-option label="感激" value="GRATITUDE" />
              <el-option label="其他" value="OTHER" />
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索倾诉内容"
              @keyup.enter="searchPosts"
            >
              <el-button slot="append" icon="el-icon-search" @click="searchPosts" />
            </el-input>
          </el-col>
        </el-row>
      </div>

      <!-- 倾诉列表 -->
      <div class="posts-section">
        <div v-loading="loading" class="posts-list">
          <div v-for="post in posts" :key="post.id" class="post-item">
            <el-card class="post-card" @click.native="viewPost(post.id)">
              <div class="post-header">
                <div class="post-author">
                  <i class="el-icon-user"></i>
                  {{ post.anonymousNickname }}
                </div>
                <div class="post-meta">
                  <el-tag v-if="post.emotionTag" :type="getEmotionTagType(post.emotionTag)" size="mini">
                    {{ getEmotionTagText(post.emotionTag) }}
                  </el-tag>
                  <span class="post-time">{{ formatTime(post.createdAt) }}</span>
                </div>
              </div>

              <h3 class="post-title">{{ post.title }}</h3>
              <p class="post-content">{{ post.content.substring(0, 200) }}...</p>

              <div class="post-footer">
                <div class="post-stats">
                  <span><i class="el-icon-view"></i> {{ post.viewCount }}</span>
                  <span><i class="el-icon-chat-dot-square"></i> {{ post.replyCount }}</span>
                  <span><i class="el-icon-heart"></i> {{ post.likeCount }}</span>
                </div>
                <div class="post-actions">
                  <el-tag v-if="post.isCounselorReplied" type="success" size="mini">
                    <i class="el-icon-medal"></i> 咨询师已回复
                  </el-tag>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            @current-change="handlePageChange"
            :current-page="currentPage + 1"
            :page-size="pageSize"
            :total="totalElements"
            layout="prev, pager, next, total"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'AnonymousWall',
  data() {
    return {
      loading: false,
      publishing: false,
      posts: [],
      currentPage: 0,
      pageSize: 10,
      totalElements: 0,
      filterEmotion: '',
      searchKeyword: '',
      postForm: {
        title: '',
        content: '',
        emotionTag: '',
        anonymousNickname: '',
        isAnonymous: true
      },
      postRules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { max: 2000, message: '内容长度不能超过2000个字符', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.loadPosts()
  },
  methods: {
    async loadPosts() {
      this.loading = true
      try {
        let url = `/posts?page=${this.currentPage}&size=${this.pageSize}`
        if (this.filterEmotion) {
          url += `&emotionTag=${this.filterEmotion}`
        }

        const response = await this.$http.get(url)
        if (response.success) {
          this.posts = response.data.content
          this.totalElements = response.data.totalElements
        }
      } catch (error) {
        this.$message.error('加载倾诉列表失败')
      } finally {
        this.loading = false
      }
    },

    async searchPosts() {
      if (!this.searchKeyword.trim()) {
        this.loadPosts()
        return
      }

      this.loading = true
      try {
        const response = await this.$http.get(`/posts/search?keyword=${this.searchKeyword}&page=${this.currentPage}&size=${this.pageSize}`)
        if (response.success) {
          this.posts = response.data.content
          this.totalElements = response.data.totalElements
        }
      } catch (error) {
        this.$message.error('搜索失败')
      } finally {
        this.loading = false
      }
    },

    publishPost() {
      this.$refs.postForm.validate(async (valid) => {
        if (valid) {
          this.publishing = true
          try {
            const response = await this.$http.post('/posts', this.postForm)
            if (response.success) {
              this.$message.success('发布成功')
              this.resetForm()
              this.loadPosts()
            }
          } catch (error) {
            this.$message.error(error.response?.data?.message || '发布失败')
          } finally {
            this.publishing = false
          }
        }
      })
    },

    resetForm() {
      this.$refs.postForm.resetFields()
      this.postForm = {
        title: '',
        content: '',
        emotionTag: '',
        anonymousNickname: '',
        isAnonymous: true
      }
    },

    viewPost(postId) {
      this.$router.push(`/post/${postId}`)
    },

    handlePageChange(page) {
      this.currentPage = page - 1
      this.loadPosts()
    },

    formatTime(time) {
      return moment(time).fromNow()
    },

    getEmotionTagType(tag) {
      const typeMap = {
        'DEPRESSION': 'info',
        'ANXIETY': 'warning',
        'STRESS': 'danger',
        'LONELINESS': 'info',
        'ANGER': 'danger',
        'CONFUSION': 'warning',
        'SADNESS': 'info',
        'FEAR': 'warning',
        'HOPE': 'success',
        'GRATITUDE': 'success',
        'OTHER': ''
      }
      return typeMap[tag] || ''
    },

    getEmotionTagText(tag) {
      const textMap = {
        'DEPRESSION': '抑郁',
        'ANXIETY': '焦虑',
        'STRESS': '压力',
        'LONELINESS': '孤独',
        'ANGER': '愤怒',
        'CONFUSION': '困惑',
        'SADNESS': '悲伤',
        'FEAR': '恐惧',
        'HOPE': '希望',
        'GRATITUDE': '感激',
        'OTHER': '其他'
      }
      return textMap[tag] || tag
    }
  }
}
</script>

<style scoped>
.anonymous-wall {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  color: #333;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

.post-form-section {
  margin-bottom: 40px;
}

.post-form-card {
  max-width: 800px;
  margin: 0 auto;
}

.filter-section {
  margin-bottom: 30px;
}

.posts-section {
  margin-bottom: 40px;
}

.posts-list {
  min-height: 400px;
}

.post-item {
  margin-bottom: 20px;
}

.post-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.post-card:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.post-author {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 0.9rem;
}

.post-author i {
  margin-right: 5px;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.post-time {
  color: #999;
  font-size: 0.8rem;
}

.post-title {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
  line-height: 1.4;
}

.post-content {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.post-stats {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
  color: #999;
}

.post-stats span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-section {
  text-align: center;
  margin-top: 40px;
}
</style>
