<template>
  <div class="profile">
    <div class="container">
      <div class="page-header">
        <h1>个人资料</h1>
      </div>
      
      <el-row :gutter="30">
        <!-- 基本信息 -->
        <el-col :span="16">
          <el-card>
            <div slot="header">
              <span>基本信息</span>
            </div>
            
            <el-form ref="profileForm" :model="profileForm" :rules="profileRules" label-width="100px">
              <el-form-item label="用户名">
                <el-input v-model="profileForm.username" disabled />
              </el-form-item>
              
              <el-form-item label="真实姓名" prop="realName">
                <el-input v-model="profileForm.realName" placeholder="请输入真实姓名" />
              </el-form-item>
              
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
              </el-form-item>
              
              <el-form-item label="手机号" prop="phoneNumber">
                <el-input v-model="profileForm.phoneNumber" placeholder="请输入手机号" />
              </el-form-item>
              
              <el-form-item label="个人简介" prop="bio">
                <el-input
                  v-model="profileForm.bio"
                  type="textarea"
                  placeholder="介绍一下自己..."
                  :rows="4"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" :loading="updating" @click="updateProfile">
                  保存修改
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
        
        <!-- 用户信息卡片 -->
        <el-col :span="8">
          <el-card>
            <div slot="header">
              <span>用户信息</span>
            </div>
            
            <div class="user-info">
              <div class="avatar">
                <img v-if="currentUser.avatarUrl" :src="currentUser.avatarUrl" :alt="currentUser.username" />
                <i v-else class="el-icon-user-solid"></i>
              </div>
              
              <div class="info">
                <h3>{{ currentUser.username }}</h3>
                <p class="role">{{ getRoleText(currentUser.role) }}</p>
                <p class="join-time">注册时间：{{ formatTime(currentUser.createdAt) }}</p>
                <p class="last-login">最后登录：{{ formatTime(currentUser.lastLoginAt) }}</p>
              </div>
            </div>
          </el-card>
          
          <!-- 咨询师专业信息 -->
          <el-card v-if="isCounselor" style="margin-top: 20px;">
            <div slot="header">
              <span>专业信息</span>
            </div>
            
            <div class="professional-info">
              <p><strong>职业头衔：</strong>{{ currentUser.professionalTitle }}</p>
              <p><strong>从业年限：</strong>{{ currentUser.yearsOfExperience }}年</p>
              <p><strong>专业领域：</strong>{{ currentUser.specialization }}</p>
              <p><strong>咨询费用：</strong>¥{{ currentUser.consultationFee }}/小时</p>
              <p><strong>当前状态：</strong>
                <el-tag :type="currentUser.isAvailable ? 'success' : 'danger'" size="mini">
                  {{ currentUser.isAvailable ? '可预约' : '暂不可预约' }}
                </el-tag>
              </p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import moment from 'moment'

export default {
  name: 'Profile',
  data() {
    return {
      updating: false,
      profileForm: {
        username: '',
        realName: '',
        email: '',
        phoneNumber: '',
        bio: ''
      },
      profileRules: {
        email: [
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        phoneNumber: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['currentUser', 'isCounselor'])
  },
  mounted() {
    this.initForm()
  },
  methods: {
    initForm() {
      if (this.currentUser) {
        this.profileForm = {
          username: this.currentUser.username,
          realName: this.currentUser.realName || '',
          email: this.currentUser.email || '',
          phoneNumber: this.currentUser.phoneNumber || '',
          bio: this.currentUser.bio || ''
        }
      }
    },
    
    updateProfile() {
      this.$refs.profileForm.validate(async (valid) => {
        if (valid) {
          this.updating = true
          try {
            // 这里应该调用更新用户信息的API
            // const response = await this.$http.put(`/users/${this.currentUser.id}`, this.profileForm)
            this.$message.success('个人资料更新成功')
            // 更新store中的用户信息
            // this.$store.dispatch('updateUser', response.data)
          } catch (error) {
            this.$message.error('更新失败，请重试')
          } finally {
            this.updating = false
          }
        }
      })
    },
    
    formatTime(time) {
      return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '未知'
    },
    
    getRoleText(role) {
      const roleMap = {
        'USER': '普通用户',
        'COUNSELOR': '心理咨询师',
        'ADMIN': '管理员'
      }
      return roleMap[role] || role
    }
  }
}
</script>

<style scoped>
.profile {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  color: #333;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.user-info {
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar i {
  font-size: 50px;
  color: #ccc;
}

.info h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.info p {
  color: #666;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.role {
  color: #409EFF !important;
  font-weight: 500;
}

.professional-info p {
  color: #666;
  margin-bottom: 10px;
  line-height: 1.6;
}

.professional-info strong {
  color: #333;
}
</style>
