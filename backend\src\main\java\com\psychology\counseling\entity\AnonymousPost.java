package com.psychology.counseling.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "anonymous_posts")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnonymousPost {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "标题不能为空")
    @Size(max = 100, message = "标题长度不能超过100个字符")
    @Column(nullable = false)
    private String title;
    
    @NotBlank(message = "内容不能为空")
    @Size(max = 2000, message = "内容长度不能超过2000个字符")
    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;
    
    // 发布者ID（可为空，支持完全匿名）
    @Column(name = "author_id")
    private Long authorId;
    
    // 匿名昵称
    @Column(name = "anonymous_nickname")
    private String anonymousNickname;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PostStatus status = PostStatus.PUBLISHED;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "emotion_tag")
    private EmotionTag emotionTag;
    
    @Column(name = "view_count")
    private Integer viewCount = 0;
    
    @Column(name = "like_count")
    private Integer likeCount = 0;
    
    @Column(name = "reply_count")
    private Integer replyCount = 0;
    
    @Column(name = "is_counselor_replied")
    private Boolean isCounselorReplied = false;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 回复列表
    @OneToMany(mappedBy = "post", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PostReply> replies;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (anonymousNickname == null) {
            anonymousNickname = "匿名用户" + System.currentTimeMillis() % 10000;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
