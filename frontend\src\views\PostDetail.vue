<template>
  <div class="post-detail">
    <div class="container">
      <div v-loading="loading">
        <!-- 返回按钮 -->
        <div class="back-button">
          <el-button @click="$router.go(-1)" icon="el-icon-arrow-left">
            返回
          </el-button>
        </div>
        
        <!-- 帖子内容 -->
        <div v-if="post" class="post-content">
          <el-card class="post-card">
            <div class="post-header">
              <div class="post-author">
                <i class="el-icon-user"></i>
                {{ post.anonymousNickname }}
              </div>
              <div class="post-meta">
                <el-tag v-if="post.emotionTag" :type="getEmotionTagType(post.emotionTag)" size="small">
                  {{ getEmotionTagText(post.emotionTag) }}
                </el-tag>
                <span class="post-time">{{ formatTime(post.createdAt) }}</span>
              </div>
            </div>
            
            <h1 class="post-title">{{ post.title }}</h1>
            <div class="post-content-text">{{ post.content }}</div>
            
            <div class="post-footer">
              <div class="post-stats">
                <span><i class="el-icon-view"></i> {{ post.viewCount }}</span>
                <span><i class="el-icon-chat-line-square"></i> {{ post.replyCount }}</span>
                <span><i class="el-icon-star-on"></i> {{ post.likeCount }}</span>
              </div>
              <div class="post-actions">
                <el-button type="primary" size="small" @click="likePost" :loading="liking">
                  <i class="el-icon-star-on"></i> 点赞
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
        
        <!-- 回复表单 -->
        <div class="reply-form-section">
          <el-card>
            <div slot="header">
              <span>发表回复</span>
            </div>
            
            <el-form ref="replyForm" :model="replyForm" :rules="replyRules" label-width="80px">
              <el-form-item label="回复内容" prop="content">
                <el-input
                  v-model="replyForm.content"
                  type="textarea"
                  placeholder="请输入您的回复..."
                  :rows="4"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
              
              <el-form-item label="回复类型" prop="replyType">
                <el-select v-model="replyForm.replyType" placeholder="选择回复类型">
                  <el-option label="支持鼓励" value="SUPPORT" />
                  <el-option label="建议指导" value="ADVICE" />
                  <el-option label="经验分享" value="SHARE" />
                  <el-option label="询问了解" value="QUESTION" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="昵称" prop="anonymousNickname">
                <el-input
                  v-model="replyForm.anonymousNickname"
                  placeholder="匿名昵称（可选）"
                  maxlength="20"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" :loading="replying" @click="submitReply">
                  发表回复
                </el-button>
                <el-button @click="resetReplyForm">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
        
        <!-- 回复列表 -->
        <div class="replies-section">
          <el-card>
            <div slot="header">
              <span>回复列表 ({{ replies.length }})</span>
            </div>
            
            <div v-if="replies.length === 0" class="no-replies">
              <p>暂无回复，快来发表第一个回复吧！</p>
            </div>
            
            <div v-else class="replies-list">
              <div v-for="reply in replies" :key="reply.id" class="reply-item">
                <div class="reply-header">
                  <div class="reply-author">
                    <i class="el-icon-user"></i>
                    {{ reply.anonymousNickname }}
                    <el-tag v-if="reply.isCounselorReply" type="success" size="mini">
                      <i class="el-icon-medal"></i> 咨询师
                    </el-tag>
                  </div>
                  <div class="reply-meta">
                    <el-tag :type="getReplyTypeColor(reply.replyType)" size="mini">
                      {{ getReplyTypeText(reply.replyType) }}
                    </el-tag>
                    <span class="reply-time">{{ formatTime(reply.createdAt) }}</span>
                  </div>
                </div>
                
                <div class="reply-content">{{ reply.content }}</div>
                
                <div class="reply-footer">
                  <div class="reply-stats">
                    <span><i class="el-icon-star-on"></i> {{ reply.likeCount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'PostDetail',
  props: ['id'],
  data() {
    return {
      loading: false,
      liking: false,
      replying: false,
      post: null,
      replies: [],
      replyForm: {
        content: '',
        replyType: 'SUPPORT',
        anonymousNickname: '',
        isAnonymous: true
      },
      replyRules: {
        content: [
          { required: true, message: '请输入回复内容', trigger: 'blur' },
          { max: 500, message: '回复内容长度不能超过500个字符', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.loadPost()
    this.loadReplies()
  },
  methods: {
    async loadPost() {
      this.loading = true
      try {
        const response = await this.$http.get(`/posts/${this.id}`)
        if (response.success) {
          this.post = response.data
        }
      } catch (error) {
        this.$message.error('加载帖子失败')
        this.$router.go(-1)
      } finally {
        this.loading = false
      }
    },

    async loadReplies() {
      try {
        const response = await this.$http.get(`/posts/${this.id}/replies`)
        if (response.success) {
          this.replies = response.data
        }
      } catch (error) {
        console.error('加载回复失败:', error)
      }
    },

    async likePost() {
      this.liking = true
      try {
        const response = await this.$http.post(`/posts/${this.id}/like`)
        if (response.success) {
          this.post.likeCount++
          this.$message.success('点赞成功')
        }
      } catch (error) {
        this.$message.error('点赞失败')
      } finally {
        this.liking = false
      }
    },

    submitReply() {
      this.$refs.replyForm.validate(async (valid) => {
        if (valid) {
          this.replying = true
          try {
            const response = await this.$http.post(`/posts/${this.id}/replies`, this.replyForm)
            if (response.success) {
              this.$message.success('回复成功')
              this.resetReplyForm()
              this.loadReplies()
              this.post.replyCount++
            }
          } catch (error) {
            this.$message.error(error.response?.data?.message || '回复失败')
          } finally {
            this.replying = false
          }
        }
      })
    },

    resetReplyForm() {
      this.$refs.replyForm.resetFields()
      this.replyForm = {
        content: '',
        replyType: 'SUPPORT',
        anonymousNickname: '',
        isAnonymous: true
      }
    },

    formatTime(time) {
      return moment(time).format('YYYY-MM-DD HH:mm:ss')
    },

    getEmotionTagType(tag) {
      const typeMap = {
        'DEPRESSION': 'info',
        'ANXIETY': 'warning',
        'STRESS': 'danger',
        'LONELINESS': 'info',
        'ANGER': 'danger',
        'CONFUSION': 'warning',
        'SADNESS': 'info',
        'FEAR': 'warning',
        'HOPE': 'success',
        'GRATITUDE': 'success',
        'OTHER': ''
      }
      return typeMap[tag] || ''
    },

    getEmotionTagText(tag) {
      const textMap = {
        'DEPRESSION': '抑郁',
        'ANXIETY': '焦虑',
        'STRESS': '压力',
        'LONELINESS': '孤独',
        'ANGER': '愤怒',
        'CONFUSION': '困惑',
        'SADNESS': '悲伤',
        'FEAR': '恐惧',
        'HOPE': '希望',
        'GRATITUDE': '感激',
        'OTHER': '其他'
      }
      return textMap[tag] || tag
    },

    getReplyTypeColor(type) {
      const colorMap = {
        'SUPPORT': 'success',
        'ADVICE': 'primary',
        'SHARE': 'info',
        'QUESTION': 'warning',
        'PROFESSIONAL': 'danger'
      }
      return colorMap[type] || ''
    },

    getReplyTypeText(type) {
      const textMap = {
        'SUPPORT': '支持鼓励',
        'ADVICE': '建议指导',
        'SHARE': '经验分享',
        'QUESTION': '询问了解',
        'PROFESSIONAL': '专业干预'
      }
      return textMap[type] || type
    }
  }
}
</script>

<style scoped>
.post-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.back-button {
  margin-bottom: 20px;
}

.post-content {
  margin-bottom: 30px;
}

.post-card {
  margin-bottom: 20px;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.post-author {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 0.9rem;
}

.post-author i {
  margin-right: 5px;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.post-time {
  color: #999;
  font-size: 0.8rem;
}

.post-title {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.8rem;
  line-height: 1.4;
}

.post-content-text {
  color: #666;
  line-height: 1.8;
  margin-bottom: 20px;
  font-size: 1.1rem;
  white-space: pre-wrap;
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.post-stats {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
  color: #999;
}

.post-stats span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.reply-form-section {
  margin-bottom: 30px;
}

.replies-section {
  margin-bottom: 30px;
}

.no-replies {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.replies-list {
  max-height: 600px;
  overflow-y: auto;
}

.reply-item {
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.reply-author {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 0.9rem;
}

.reply-author i {
  margin-right: 5px;
}

.reply-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.reply-time {
  color: #999;
  font-size: 0.8rem;
}

.reply-content {
  color: #333;
  line-height: 1.6;
  margin-bottom: 10px;
  white-space: pre-wrap;
}

.reply-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reply-stats {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: #999;
}

.reply-stats span {
  display: flex;
  align-items: center;
  gap: 5px;
}
</style>
