package com.psychology.counseling.controller;

import com.psychology.counseling.dto.ApiResponse;
import com.psychology.counseling.dto.UserLoginDto;
import com.psychology.counseling.dto.UserRegistrationDto;
import com.psychology.counseling.entity.User;
import com.psychology.counseling.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AuthController {
    
    private final UserService userService;
    
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<User>> register(@Valid @RequestBody UserRegistrationDto registrationDto) {
        try {
            User user = userService.register(registrationDto);
            return ResponseEntity.ok(ApiResponse.success("注册成功", user));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> login(@Valid @RequestBody UserLoginDto loginDto) {
        try {
            Map<String, Object> result = userService.login(loginDto);
            return ResponseEntity.ok(ApiResponse.success("登录成功", result));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<String>> logout() {
        // 在实际应用中，这里可以实现token黑名单等逻辑
        return ResponseEntity.ok(ApiResponse.success("退出登录成功"));
    }
    
    @GetMapping("/test")
    public ResponseEntity<ApiResponse<String>> test() {
        return ResponseEntity.ok(ApiResponse.success("API连接正常"));
    }
}
