package com.psychology.counseling.service;

import com.psychology.counseling.dto.UserLoginDto;
import com.psychology.counseling.dto.UserRegistrationDto;
import com.psychology.counseling.entity.User;
import com.psychology.counseling.entity.UserRole;
import com.psychology.counseling.entity.UserStatus;
import com.psychology.counseling.repository.UserRepository;
import com.psychology.counseling.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
public class UserService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    
    public User register(UserRegistrationDto registrationDto) {
        // 验证用户名是否已存在
        if (userRepository.existsByUsername(registrationDto.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 验证邮箱是否已存在
        if (registrationDto.getEmail() != null && userRepository.existsByEmail(registrationDto.getEmail())) {
            throw new RuntimeException("邮箱已被注册");
        }
        
        // 验证密码确认
        if (!registrationDto.getPassword().equals(registrationDto.getConfirmPassword())) {
            throw new RuntimeException("两次输入的密码不一致");
        }
        
        // 创建用户
        User user = new User();
        user.setUsername(registrationDto.getUsername());
        user.setPassword(passwordEncoder.encode(registrationDto.getPassword()));
        user.setEmail(registrationDto.getEmail());
        user.setPhoneNumber(registrationDto.getPhoneNumber());
        user.setRealName(registrationDto.getRealName());
        user.setRole(registrationDto.getRole());
        user.setStatus(UserStatus.ACTIVE);
        
        // 如果是咨询师，设置专业信息
        if (registrationDto.getRole() == UserRole.COUNSELOR) {
            user.setProfessionalTitle(registrationDto.getProfessionalTitle());
            user.setQualificationCertificate(registrationDto.getQualificationCertificate());
            user.setYearsOfExperience(registrationDto.getYearsOfExperience());
            user.setSpecialization(registrationDto.getSpecialization());
            user.setConsultationFee(registrationDto.getConsultationFee());
            user.setIsAvailable(true);
        }
        
        return userRepository.save(user);
    }
    
    public Map<String, Object> login(UserLoginDto loginDto) {
        // 查找用户
        Optional<User> userOptional = userRepository.findByUsername(loginDto.getUsername());
        if (!userOptional.isPresent()) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        User user = userOptional.get();
        
        // 验证密码
        if (!passwordEncoder.matches(loginDto.getPassword(), user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 检查用户状态
        if (user.getStatus() != UserStatus.ACTIVE) {
            throw new RuntimeException("账户已被禁用，请联系管理员");
        }
        
        // 更新最后登录时间
        user.setLastLoginAt(LocalDateTime.now());
        userRepository.save(user);
        
        // 生成JWT token
        String token = jwtUtil.generateToken(user.getUsername());
        
        // 返回登录信息
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", user);
        
        return result;
    }
    
    public User findByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
    }
    
    public User findById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
    }
    
    public List<User> getAvailableCounselors() {
        return userRepository.findAvailableCounselors();
    }
    
    public User updateProfile(Long userId, User updateUser) {
        User user = findById(userId);
        
        if (updateUser.getRealName() != null) {
            user.setRealName(updateUser.getRealName());
        }
        if (updateUser.getEmail() != null) {
            user.setEmail(updateUser.getEmail());
        }
        if (updateUser.getPhoneNumber() != null) {
            user.setPhoneNumber(updateUser.getPhoneNumber());
        }
        if (updateUser.getBio() != null) {
            user.setBio(updateUser.getBio());
        }
        if (updateUser.getAvatarUrl() != null) {
            user.setAvatarUrl(updateUser.getAvatarUrl());
        }
        
        return userRepository.save(user);
    }
}
