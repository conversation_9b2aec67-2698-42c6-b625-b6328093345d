package com.psychology.counseling.controller;

import com.psychology.counseling.dto.ApiResponse;
import com.psychology.counseling.dto.AnonymousPostDto;
import com.psychology.counseling.dto.PostReplyDto;
import com.psychology.counseling.entity.AnonymousPost;
import com.psychology.counseling.entity.EmotionTag;
import com.psychology.counseling.entity.PostReply;
import com.psychology.counseling.service.AnonymousPostService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/posts")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AnonymousPostController {
    
    private final AnonymousPostService postService;
    
    @PostMapping
    public ResponseEntity<ApiResponse<AnonymousPost>> createPost(
            @Valid @RequestBody AnonymousPostDto postDto,
            HttpServletRequest request) {
        try {
            AnonymousPost post = postService.createPost(postDto, request);
            return ResponseEntity.ok(ApiResponse.success("发布成功", post));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    @GetMapping
    public ResponseEntity<ApiResponse<Page<AnonymousPost>>> getPosts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) EmotionTag emotionTag) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<AnonymousPost> posts = postService.getPosts(pageable, emotionTag);
            return ResponseEntity.ok(ApiResponse.success(posts));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<AnonymousPost>> getPost(@PathVariable Long id) {
        try {
            AnonymousPost post = postService.getPostById(id);
            return ResponseEntity.ok(ApiResponse.success(post));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    @PostMapping("/{id}/replies")
    public ResponseEntity<ApiResponse<PostReply>> replyToPost(
            @PathVariable Long id,
            @Valid @RequestBody PostReplyDto replyDto,
            HttpServletRequest request) {
        try {
            replyDto.setPostId(id);
            PostReply reply = postService.replyToPost(replyDto, request);
            return ResponseEntity.ok(ApiResponse.success("回复成功", reply));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    @GetMapping("/{id}/replies")
    public ResponseEntity<ApiResponse<List<PostReply>>> getPostReplies(@PathVariable Long id) {
        try {
            List<PostReply> replies = postService.getPostReplies(id);
            return ResponseEntity.ok(ApiResponse.success(replies));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    @PostMapping("/{id}/like")
    public ResponseEntity<ApiResponse<String>> likePost(@PathVariable Long id) {
        try {
            postService.likePost(id);
            return ResponseEntity.ok(ApiResponse.success("点赞成功"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<AnonymousPost>>> searchPosts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<AnonymousPost> posts = postService.searchPosts(keyword, pageable);
            return ResponseEntity.ok(ApiResponse.success(posts));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
}
