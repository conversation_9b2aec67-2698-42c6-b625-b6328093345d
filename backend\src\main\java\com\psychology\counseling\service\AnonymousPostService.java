package com.psychology.counseling.service;

import com.psychology.counseling.dto.AnonymousPostDto;
import com.psychology.counseling.dto.PostReplyDto;
import com.psychology.counseling.entity.*;
import com.psychology.counseling.repository.AnonymousPostRepository;
import com.psychology.counseling.repository.PostReplyRepository;
import com.psychology.counseling.repository.UserRepository;
import com.psychology.counseling.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
public class AnonymousPostService {
    
    private final AnonymousPostRepository postRepository;
    private final PostReplyRepository replyRepository;
    private final UserRepository userRepository;
    private final JwtUtil jwtUtil;
    
    public AnonymousPost createPost(AnonymousPostDto postDto, HttpServletRequest request) {
        AnonymousPost post = new AnonymousPost();
        post.setTitle(postDto.getTitle());
        post.setContent(postDto.getContent());
        post.setEmotionTag(postDto.getEmotionTag());
        post.setStatus(PostStatus.PUBLISHED);
        
        // 处理匿名发布
        if (postDto.getIsAnonymous()) {
            post.setAnonymousNickname(postDto.getAnonymousNickname());
            // 不设置authorId，保持完全匿名
        } else {
            // 从token中获取用户信息
            String token = extractTokenFromRequest(request);
            if (token != null) {
                String username = jwtUtil.getUsernameFromToken(token);
                Optional<User> userOptional = userRepository.findByUsername(username);
                if (userOptional.isPresent()) {
                    post.setAuthorId(userOptional.get().getId());
                    post.setAnonymousNickname(postDto.getAnonymousNickname());
                }
            }
        }
        
        return postRepository.save(post);
    }
    
    public Page<AnonymousPost> getPosts(Pageable pageable, EmotionTag emotionTag) {
        if (emotionTag != null) {
            return postRepository.findByStatusAndEmotionTagOrderByCreatedAtDesc(
                    PostStatus.PUBLISHED, emotionTag, pageable);
        } else {
            return postRepository.findByStatusOrderByCreatedAtDesc(PostStatus.PUBLISHED, pageable);
        }
    }
    
    public AnonymousPost getPostById(Long id) {
        AnonymousPost post = postRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        // 增加浏览量
        postRepository.incrementViewCount(id);
        
        return post;
    }
    
    public PostReply replyToPost(PostReplyDto replyDto, HttpServletRequest request) {
        // 验证帖子是否存在
        AnonymousPost post = postRepository.findById(replyDto.getPostId())
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        PostReply reply = new PostReply();
        reply.setPost(post);
        reply.setContent(replyDto.getContent());
        reply.setReplyType(replyDto.getReplyType());
        
        // 处理匿名回复
        if (replyDto.getIsAnonymous()) {
            reply.setAnonymousNickname(replyDto.getAnonymousNickname());
            // 不设置replierId，保持匿名
        } else {
            // 从token中获取用户信息
            String token = extractTokenFromRequest(request);
            if (token != null) {
                String username = jwtUtil.getUsernameFromToken(token);
                Optional<User> userOptional = userRepository.findByUsername(username);
                if (userOptional.isPresent()) {
                    User user = userOptional.get();
                    reply.setReplierId(user.getId());
                    reply.setAnonymousNickname(replyDto.getAnonymousNickname());
                    
                    // 如果是咨询师回复，标记为专业回复
                    if (user.getRole() == UserRole.COUNSELOR) {
                        reply.setIsCounselorReply(true);
                        reply.setReplyType(ReplyType.PROFESSIONAL);
                        
                        // 更新帖子的咨询师回复状态
                        post.setIsCounselorReplied(true);
                        postRepository.save(post);
                    }
                }
            }
        }
        
        PostReply savedReply = replyRepository.save(reply);
        
        // 更新帖子回复数量
        post.setReplyCount(post.getReplyCount() + 1);
        postRepository.save(post);
        
        return savedReply;
    }
    
    public List<PostReply> getPostReplies(Long postId) {
        return replyRepository.findByPostIdOrderByCreatedAtAsc(postId);
    }
    
    public void likePost(Long postId) {
        if (!postRepository.existsById(postId)) {
            throw new RuntimeException("帖子不存在");
        }
        postRepository.incrementLikeCount(postId);
    }
    
    public Page<AnonymousPost> searchPosts(String keyword, Pageable pageable) {
        return postRepository.searchPosts(PostStatus.PUBLISHED, keyword, pageable);
    }
    
    public Page<AnonymousPost> getPostsNeedingCounselorAttention(Pageable pageable) {
        return postRepository.findPostsNeedingCounselorAttention(pageable);
    }
    
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
