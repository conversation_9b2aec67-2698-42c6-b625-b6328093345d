package com.psychology.counseling.repository;

import com.psychology.counseling.entity.AnonymousPost;
import com.psychology.counseling.entity.EmotionTag;
import com.psychology.counseling.entity.PostStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AnonymousPostRepository extends JpaRepository<AnonymousPost, Long> {
    
    Page<AnonymousPost> findByStatusOrderByCreatedAtDesc(PostStatus status, Pageable pageable);
    
    Page<AnonymousPost> findByStatusAndEmotionTagOrderByCreatedAtDesc(PostStatus status, EmotionTag emotionTag, Pageable pageable);
    
    List<AnonymousPost> findByAuthorIdAndStatusOrderByCreatedAtDesc(Long authorId, PostStatus status);
    
    @Query("SELECT p FROM AnonymousPost p WHERE p.status = :status AND (p.title LIKE %:keyword% OR p.content LIKE %:keyword%) ORDER BY p.createdAt DESC")
    Page<AnonymousPost> searchPosts(@Param("status") PostStatus status, @Param("keyword") String keyword, Pageable pageable);
    
    @Query("SELECT p FROM AnonymousPost p WHERE p.status = 'PUBLISHED' AND p.isCounselorReplied = false ORDER BY p.createdAt DESC")
    Page<AnonymousPost> findPostsNeedingCounselorAttention(Pageable pageable);
    
    @Modifying
    @Query("UPDATE AnonymousPost p SET p.viewCount = p.viewCount + 1 WHERE p.id = :id")
    void incrementViewCount(@Param("id") Long id);
    
    @Modifying
    @Query("UPDATE AnonymousPost p SET p.likeCount = p.likeCount + 1 WHERE p.id = :id")
    void incrementLikeCount(@Param("id") Long id);
    
    @Query("SELECT COUNT(p) FROM AnonymousPost p WHERE p.createdAt >= :startTime AND p.createdAt <= :endTime AND p.status = 'PUBLISHED'")
    Long countPostsByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
