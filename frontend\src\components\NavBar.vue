<template>
  <div class="navbar">
    <div class="nav-container">
      <!-- Logo和标题 -->
      <div class="nav-brand">
        <router-link to="/" class="brand-link">
          <i class="el-icon-chat-dot-round"></i>
          心理咨询平台
        </router-link>
      </div>
      
      <!-- 导航菜单 -->
      <div class="nav-menu">
        <el-menu
          mode="horizontal"
          background-color="#409EFF"
          text-color="white"
          active-text-color="#ffd04b"
          :default-active="activeIndex"
          @select="handleSelect"
        >
          <el-menu-item index="/">首页</el-menu-item>
          <el-menu-item index="/anonymous-wall">倾诉墙</el-menu-item>
          <el-menu-item index="/appointment" v-if="isLoggedIn">预约咨询</el-menu-item>
        </el-menu>
      </div>
      
      <!-- 用户操作区域 -->
      <div class="nav-user">
        <div v-if="!isLoggedIn" class="auth-buttons">
          <el-button type="text" @click="$router.push('/login')" style="color: white;">
            登录
          </el-button>
          <el-button type="text" @click="$router.push('/register')" style="color: white;">
            注册
          </el-button>
        </div>
        
        <div v-else class="user-info">
          <el-dropdown @command="handleCommand">
            <span class="user-dropdown">
              <i class="el-icon-user"></i>
              {{ currentUser.username }}
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'NavBar',
  computed: {
    ...mapGetters(['isLoggedIn', 'currentUser']),
    activeIndex() {
      return this.$route.path
    }
  },
  methods: {
    handleSelect(key) {
      if (key !== this.$route.path) {
        this.$router.push(key)
      }
    },
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/profile')
          break
        case 'logout':
          this.logout()
          break
      }
    },
    logout() {
      this.$store.dispatch('logout')
      this.$message.success('退出登录成功')
      this.$router.push('/')
    }
  }
}
</script>

<style scoped>
.navbar {
  width: 100%;
  height: 60px;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.nav-brand {
  flex-shrink: 0;
}

.brand-link {
  color: white;
  text-decoration: none;
  font-size: 20px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.brand-link i {
  margin-right: 8px;
  font-size: 24px;
}

.nav-menu {
  flex: 1;
  margin: 0 40px;
}

.nav-user {
  flex-shrink: 0;
}

.auth-buttons .el-button {
  margin-left: 10px;
}

.user-dropdown {
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.user-dropdown i {
  margin: 0 4px;
}
</style>
