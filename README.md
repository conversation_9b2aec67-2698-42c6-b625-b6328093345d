# 心理咨询与匿名倾诉平台

基于Spring Boot与Vue的心理咨询与匿名倾诉平台，提供匿名倾诉、专业咨询预约等功能。

## 项目特色

- 🗨️ **匿名倾诉墙**：完全匿名的倾诉环境，用户可以安全地分享内心困扰
- 👨‍⚕️ **专业咨询**：资深心理咨询师提供一对一专业咨询服务
- 💬 **温暖回复**：社区用户和咨询师可以提供支持和建议
- 🏷️ **情感标签**：按情感类型分类倾诉内容
- 📅 **预约系统**：在线预约心理咨询时间段

## 技术栈

### 后端
- Spring Boot 2.7.14
- Spring Security
- Spring Data JPA
- MySQL / H2 Database
- JWT认证
- Maven

### 前端
- Vue 2.6
- Element UI
- Vue Router
- Vuex
- Axios
- Moment.js

## 项目结构

```
心理咨询和匿名倾诉/
├── backend/                 # 后端Spring Boot项目
│   ├── src/main/java/
│   │   └── com/psychology/counseling/
│   │       ├── entity/      # 实体类
│   │       ├── repository/  # 数据访问层
│   │       ├── service/     # 业务逻辑层
│   │       ├── controller/  # 控制器层
│   │       ├── dto/         # 数据传输对象
│   │       ├── config/      # 配置类
│   │       └── util/        # 工具类
│   ├── src/main/resources/
│   │   └── application.yml  # 应用配置
│   └── pom.xml             # Maven配置
├── frontend/               # 前端Vue项目
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── router/         # 路由配置
│   │   ├── store/          # 状态管理
│   │   └── main.js         # 入口文件
│   ├── public/
│   ├── package.json        # 依赖配置
│   └── vue.config.js       # Vue配置
└── README.md
```

## 快速开始

### 环境要求
- Java 8+
- Node.js 14+
- Maven 3.6+

### 后端启动

1. 进入后端目录：
```bash
cd backend
```

2. 安装依赖并启动：
```bash
mvn clean install
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动

### 前端启动

1. 进入前端目录：
```bash
cd frontend
```

2. 安装依赖：
```bash
npm install
```

3. 启动开发服务器：
```bash
npm run serve
```

前端应用将在 http://localhost:3000 启动

## 功能模块

### 1. 用户模块
- 用户注册/登录
- 角色管理（普通用户、心理咨询师、管理员）
- 个人资料管理

### 2. 匿名倾诉墙
- 匿名发布倾诉内容
- 按情感标签分类
- 回复和点赞功能
- 搜索和筛选

### 3. 咨询预约模块
- 查看可用咨询师
- 在线预约咨询时间
- 预约状态管理

### 4. 回复系统
- 匿名回复倾诉
- 咨询师专业回复
- 回复类型分类

## 数据库设计

主要数据表：
- `users` - 用户信息
- `anonymous_posts` - 匿名倾诉帖子
- `post_replies` - 帖子回复
- `appointments` - 咨询预约

## API接口

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户退出

### 倾诉墙接口
- `GET /api/posts` - 获取倾诉列表
- `POST /api/posts` - 发布倾诉
- `GET /api/posts/{id}` - 获取倾诉详情
- `POST /api/posts/{id}/replies` - 回复倾诉
- `POST /api/posts/{id}/like` - 点赞倾诉

### 预约接口
- `GET /api/counselors` - 获取咨询师列表
- `POST /api/appointments` - 创建预约
- `GET /api/appointments` - 获取预约列表

## 开发说明

### 后端开发
- 使用H2内存数据库进行开发测试
- JWT token用于用户认证
- 支持跨域请求
- 统一的API响应格式

### 前端开发
- 使用Element UI组件库
- 响应式设计，支持移动端
- 路由守卫保护需要登录的页面
- Axios拦截器处理请求和响应

## 部署说明

### 生产环境配置
1. 修改 `application.yml` 中的数据库配置
2. 配置JWT密钥和过期时间
3. 前端构建：`npm run build`
4. 后端打包：`mvn clean package`

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。
