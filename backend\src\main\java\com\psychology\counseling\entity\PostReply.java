package com.psychology.counseling.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Entity
@Table(name = "post_replies")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostReply {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "post_id", nullable = false)
    private AnonymousPost post;
    
    @NotBlank(message = "回复内容不能为空")
    @Size(max = 500, message = "回复内容长度不能超过500个字符")
    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;
    
    // 回复者ID（可为空，支持匿名回复）
    @Column(name = "replier_id")
    private Long replierId;
    
    // 匿名昵称
    @Column(name = "anonymous_nickname")
    private String anonymousNickname;
    
    // 是否为咨询师回复
    @Column(name = "is_counselor_reply")
    private Boolean isCounselorReply = false;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "reply_type")
    private ReplyType replyType = ReplyType.SUPPORT;
    
    @Column(name = "like_count")
    private Integer likeCount = 0;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (anonymousNickname == null) {
            anonymousNickname = "匿名回复者" + System.currentTimeMillis() % 10000;
        }
    }
}
