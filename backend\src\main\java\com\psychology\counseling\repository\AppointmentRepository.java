package com.psychology.counseling.repository;

import com.psychology.counseling.entity.Appointment;
import com.psychology.counseling.entity.AppointmentStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {
    
    List<Appointment> findByUserIdOrderByAppointmentTimeDesc(Long userId);
    
    List<Appointment> findByCounselorIdOrderByAppointmentTimeDesc(Long counselorId);
    
    Page<Appointment> findByUserIdOrderByAppointmentTimeDesc(Long userId, Pageable pageable);
    
    Page<Appointment> findByCounselorIdOrderByAppointmentTimeDesc(Long counselorId, Pageable pageable);
    
    List<Appointment> findByStatusOrderByAppointmentTimeAsc(AppointmentStatus status);
    
    @Query("SELECT a FROM Appointment a WHERE a.counselorId = :counselorId AND a.status = :status ORDER BY a.appointmentTime ASC")
    List<Appointment> findByCounselorIdAndStatus(@Param("counselorId") Long counselorId, @Param("status") AppointmentStatus status);
    
    @Query("SELECT a FROM Appointment a WHERE a.userId = :userId AND a.status = :status ORDER BY a.appointmentTime DESC")
    List<Appointment> findByUserIdAndStatus(@Param("userId") Long userId, @Param("status") AppointmentStatus status);
    
    @Query("SELECT a FROM Appointment a WHERE a.counselorId = :counselorId AND a.appointmentTime BETWEEN :startTime AND :endTime")
    List<Appointment> findByCounselorIdAndTimeRange(@Param("counselorId") Long counselorId, 
                                                   @Param("startTime") LocalDateTime startTime, 
                                                   @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT COUNT(a) FROM Appointment a WHERE a.counselorId = :counselorId AND a.appointmentTime BETWEEN :startTime AND :endTime AND a.status != 'CANCELLED'")
    Long countByCounselorIdAndTimeRange(@Param("counselorId") Long counselorId, 
                                       @Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT a FROM Appointment a WHERE a.appointmentTime <= :currentTime AND a.status = 'CONFIRMED'")
    List<Appointment> findOverdueAppointments(@Param("currentTime") LocalDateTime currentTime);
}
